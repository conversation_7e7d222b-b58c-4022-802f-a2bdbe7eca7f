<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenRobo Agentic AI Insurance Policy Suite’s Agentic Insurance Suite</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
            position: relative;
        }
        
        /* Data Sovereignty Badge */
        .sovereignty-badge {
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 1rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            z-index: 1000;
            max-width: 200px;
            text-align: center;
            font-size: 0.85rem;
            border: 2px solid rgba(255,255,255,0.3);
        }
        
        .sovereignty-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .sovereignty-text {
            font-weight: bold;
            margin-bottom: 0.3rem;
        }
        
        .sovereignty-subtext {
            font-size: 0.75rem;
            opacity: 0.9;
        }
        
        /* Login Page Styles */
        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 3rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .logo-large {
            font-size: 2rem;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 0.5rem;
        }
        
        .tagline {
            color: #666;
            margin-bottom: 2rem;
            font-size: 0.9rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
            text-align: left;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #1e3c72;
        }
        
        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        /* Main App Styles */
        .app-container {
            display: none;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1e3c72;
        }
        
        .nav-links {
            display: flex;
            gap: 1rem;
        }
        
        .nav-links button {
            background: none;
            border: none;
            color: #333;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-links button:hover,
        .nav-links button.active {
            background: #1e3c72;
            color: white;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .logout-btn {
            background: #ff6b6b;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            transform: translateY(-2px);
        }
        
        .main-content {
            max-width: 1400px;
            margin: 2rem auto;
            padding: 0 2rem;
            min-height: calc(100vh - 120px);
        }
        
        .page {
            display: none;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
        }
        
        .page.active {
            display: block;
        }
        
        /* Dashboard Styles */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .kpi-card {
            background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            transform: perspective(1000px) rotateX(0deg);
            transition: all 0.3s ease;
        }
        
        .kpi-card:hover {
            transform: perspective(1000px) rotateX(-5deg) scale(1.05);
        }
        
        .kpi-card.success { background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%); }
        .kpi-card.warning { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .kpi-card.info { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        
        .kpi-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .kpi-label {
            font-size: 1rem;
            opacity: 0.9;
        }
        
        .kpi-change {
            font-size: 0.8rem;
            margin-top: 0.5rem;
            opacity: 0.8;
        }
        
        /* Submission Processing Styles */
        .processing-layout {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1.5rem;
            height: 70vh;
        }
        
        .panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            overflow-y: auto;
        }
        
        .panel-header {
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .pdf-viewer {
            background: white;
            border: 2px dashed #ddd;
            border-radius: 10px;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            margin-bottom: 1rem;
        }
        
        .highlight-box {
            background: rgba(255, 235, 59, 0.3);
            border-left: 4px solid #ffc107;
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 5px;
        }
        
        .data-table {
            width: 100%;
            margin-bottom: 1rem;
        }
        
        .data-table tr {
            border-bottom: 1px solid #e0e0e0;
        }
        
        .data-table td {
            padding: 0.8rem;
            vertical-align: top;
        }
        
        .data-table td:first-child {
            font-weight: bold;
            color: #1e3c72;
            width: 40%;
        }
        
        .agent-decision {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 2px solid #28a745;
            border-radius: 15px;
            padding: 1.5rem;
            margin-top: 1rem;
        }
        
        .decision-header {
            font-weight: bold;
            color: #155724;
            margin-bottom: 1rem;
        }
        
        .risk-score {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 0.5rem 0;
        }
        
        .score-circle {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #28a745;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .score-circle.medium { background: #ffc107; }
        .score-circle.high { background: #dc3545; }
        
        /* Claims Triage Styles */
        .triage-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }
        
        .claim-document {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .document-content {
            line-height: 1.8;
            color: #333;
        }
        
        .highlighted-text {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%);
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .agent-analysis {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
        }
        
        .analysis-item {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .analysis-label {
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 0.5rem;
        }
        
        .analysis-value {
            font-size: 1.1rem;
            color: #333;
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .btn {
            flex: 1;
            padding: 1rem;
            border: none;
            border-radius: 10px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-approve { background: #28a745; color: white; }
        .btn-review { background: #ffc107; color: #333; }
        .btn-reject { background: #dc3545; color: white; }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        /* Audit Timeline Styles */
        .timeline {
            position: relative;
            margin: 2rem 0;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 30px;
            top: 0;
            height: 100%;
            width: 2px;
            background: #ddd;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
            padding-left: 80px;
            cursor: pointer;
        }
        
        .timeline-marker {
            position: absolute;
            left: 20px;
            top: 10px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #4ecdc4;
            border: 3px solid white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .timeline-marker.completed { background: #28a745; }
        .timeline-marker.current { background: #ffc107; animation: pulse 2s infinite; }
        
        .timeline-content {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .timeline-content:hover {
            transform: translateX(10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .timeline-title {
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 0.5rem;
        }
        
        .timeline-description {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .timeline-details {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            font-size: 0.9rem;
            color: #555;
        }
        
        /* Workflow Builder Styles */
        .workflow-canvas {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            min-height: 400px;
            position: relative;
            margin: 2rem 0;
        }
        
        .agent-node {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            display: inline-block;
            margin: 1rem;
            cursor: move;
            position: relative;
            min-width: 150px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .agent-node:hover {
            transform: scale(1.05);
        }
        
        .agent-node.human { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
        .agent-node.decision { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333; }
        
        .workflow-arrow {
            display: inline-block;
            margin: 0 1rem;
            font-size: 1.5rem;
            color: #666;
        }
        
        .agent-toolbox {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .toolbox-title {
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 1rem;
        }
        
        .toolbox-agents {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .toolbox-agent {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 0.8rem 1.2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .toolbox-agent:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        /* Model Source Indicators */
        .model-source-indicator {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.75rem;
            display: inline-block;
            margin-left: 0.5rem;
        }
        
        .model-source-indicator.private {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .model-source-indicator.hybrid {
            background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
            color: #333;
        }
        
        /* Agent Orchestration Enhancements */
        .agent-map {
            background: #f8f9fa;
            border-radius: 20px;
            padding: 2rem;
            margin: 2rem 0;
            position: relative;
        }
        
        .agent-connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, #4ecdc4, #44a08d);
            z-index: 1;
        }
        
        .agent-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin: 0 auto 0.5rem;
            position: relative;
            z-index: 2;
        }
        
        /* Reasoning Steps */
        .reasoning-steps {
            background: rgba(78, 205, 196, 0.1);
            border-left: 4px solid #4ecdc4;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .reasoning-step {
            display: flex;
            align-items: start;
            margin: 0.8rem 0;
            font-size: 0.9rem;
        }
        
        .reasoning-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #4ecdc4;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            margin-right: 0.8rem;
            flex-shrink: 0;
        }
        
        /* Trust Score Indicator */
        .trust-score {
            background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
            color: white;
            padding: 1rem;
            border-radius: 15px;
            text-align: center;
            margin: 1rem 0;
        }
        
        .trust-number {
            font-size: 2rem;
            font-weight: bold;
        }
        
        /* Anomaly Highlight */
        .anomaly-highlight {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border: 2px dashed #ff6b6b;
            padding: 0.5rem;
            border-radius: 8px;
            margin: 0.3rem 0;
            position: relative;
        }
        
        .anomaly-indicator {
            position: absolute;
            right: -10px;
            top: -10px;
            background: #ff6b6b;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        /* Test & Simulate Panel */
        .simulate-panel {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin: 2rem 0;
        }
        
        .file-drop-zone {
            border: 2px dashed rgba(255,255,255,0.5);
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            margin: 1rem 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-drop-zone:hover {
            border-color: white;
            background: rgba(255,255,255,0.1);
        }
        
        /* Agent Marketplace */
        .agent-marketplace {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .marketplace-agent {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .marketplace-agent:hover {
            transform: translateY(-5px);
            border-color: #4ecdc4;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .marketplace-agent.installed {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }
        
        .agent-rating {
            color: #ffc107;
            margin: 0.5rem 0;
        }
        
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 2rem;
            border-radius: 20px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }
        
        /* Responsive Design */
        @media (max-width: 1024px) {
            .processing-layout {
                grid-template-columns: 1fr;
                height: auto;
            }
            
            .triage-layout {
                grid-template-columns: 1fr;
            }
            
            .kpi-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .kpi-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- Login Page -->
    <div id="loginPage" class="login-container">
        <div class="login-card">
            <div class="logo-large">OpenRobo</div>
            <div class="tagline">Agentic Insurance Suite</div>
            
            <form onsubmit="login(event)">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" required value="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" required value="demo123">
                </div>
                
                <button type="submit" class="login-btn">Sign In</button>
            </form>
        </div>
    </div>
    
    <!-- Main Application -->
    <div id="mainApp" class="app-container">
        <!-- Data Sovereignty Badge -->
        <div class="sovereignty-badge">
            <div class="sovereignty-icon">🔒</div>
            <div class="sovereignty-text">100% Private</div>
            <div class="sovereignty-subtext">Your data never leaves your environment</div>
        </div>
        
        <div class="header">
            <div class="header-content">
                <div class="logo">OpenRobo Agentic AI Insurance Policy Suite’s Agentic Insurance Suite</div>
                <div class="nav-links">
                    <button onclick="showPage('dashboard')" class="active">Dashboard</button>
                    <button onclick="showPage('submissions')">Submissions</button>
                    <button onclick="showPage('claims')">Claims</button>
                    <button onclick="showPage('audit')">Audit</button>
                    <button onclick="showPage('governance')">Governance</button>
                    <button onclick="showPage('admin')">Admin</button>
                </div>
                <div class="user-menu">
                    <span>Welcome, Demo User</span>
                    <button class="logout-btn" onclick="logout()">Logout</button>
                </div>
            </div>
        </div>
        
        <div class="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard" class="page active">
                <h1 style="color: #1e3c72; margin-bottom: 2rem;">Dashboard</h1>
<div style="background: linear-gradient(135deg, #ffffff, #f1f7ff); padding: 12px; border-radius: 10px; margin-bottom: 1rem; border: 1px solid rgba(30,60,114,0.06);">
  <strong>OpenRobo’s Agentic Insurance Suite</strong> — Modular AI agents for underwriting, claims & policy operations. <em>Governance & Fairness Oversight</em> • <em>Hybrid GenAI + AI Models — cost-efficient, privately hosted (no third-party data sharing)</em>. 
</div>

                
                <div class="kpi-grid">
                    <div class="kpi-card success">
                        <div class="kpi-number">247</div>
                        <div class="kpi-label">Submissions Processed Today</div>
                        <div class="kpi-change">↑ 23% from yesterday</div>
                    </div>
                    
                    <div class="kpi-card info">
                        <div class="kpi-number">89%</div>
                        <div class="kpi-label">Touchless Processing</div>
                        <div class="kpi-change">↑ 5% from last week</div>
                    </div>
                    
                    <div class="kpi-card warning">
                        <div class="kpi-number">4.2</div>
                        <div class="kpi-label">Avg Cycle Time (hours)</div>
                        <div class="kpi-change">↓ 75% improvement</div>
                    </div>
                    
                    <div class="kpi-card">
                        <div class="kpi-number">12</div>
                        <div class="kpi-label">Fraud Alerts Flagged</div>
                        <div class="kpi-change">↓ 8% from last month</div>
                    </div>

                    <div class="kpi-card success">
                        <div class="kpi-number">98.2%</div>
                        <div class="kpi-label">Fairness Score</div>
                        <div class="kpi-change">↑ 0.3% this month</div>
                    </div>

                    <div class="kpi-card info">
                        <div class="kpi-number">0.12</div>
                        <div class="kpi-label">Bias Risk Index</div>
                        <div class="kpi-change">Within acceptable range</div>
                    </div>

                    <div class="kpi-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">
                        <div class="kpi-number">99.7%</div>
                        <div class="kpi-label">Data Sovereignty</div>
                        <div class="kpi-change">🔒 Fully Private Hosting</div>
                    </div>
                </div>

                <!-- Trust Score & Model Performance -->
                <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 2rem; margin: 2rem 0;">
                    <div class="trust-score">
                        <div class="trust-number">9.8</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">Trust Score</div>
                        <div style="font-size: 0.8rem; margin-top: 0.5rem;">Based on transparency & fairness</div>
                    </div>

                    <div style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1);">
                        <h3 style="color: #1e3c72; margin-bottom: 1rem;">Model Performance Over Time</h3>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 10px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                <span>Accuracy</span>
                                <span style="color: #28a745; font-weight: bold;">97.8% ↗</span>
                            </div>
                            <div style="background: #e9ecef; height: 4px; border-radius: 2px; margin-bottom: 1rem;">
                                <div style="background: #28a745; height: 100%; width: 97.8%; border-radius: 2px;"></div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                <span>Bias Score</span>
                                <span style="color: #28a745; font-weight: bold;">0.08 ↘</span>
                            </div>
                            <div style="background: #e9ecef; height: 4px; border-radius: 2px;">
                                <div style="background: #28a745; height: 100%; width: 8%; border-radius: 2px;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; margin-top: 2rem;">
                    <div style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1);">
                        <h3 style="color: #1e3c72; margin-bottom: 1rem;">Recent Activity</h3>
                        <div style="space-y: 1rem;">
                            <div style="border-left: 4px solid #28a745; padding: 1rem; margin-bottom: 1rem; background: #f8f9fa; border-radius: 5px;">
                                <strong>Auto-Approved:</strong> Commercial Property submission for ABC Corp - $2.1M coverage
                                <div style="color: #666; font-size: 0.9rem; margin-top: 0.5rem;">2 minutes ago</div>
                            </div>
                            <div style="border-left: 4px solid #ffc107; padding: 1rem; margin-bottom: 1rem; background: #f8f9fa; border-radius: 5px;">
                                <strong>Review Required:</strong> High-risk manufacturing submission flagged for underwriter review
                                <div style="color: #666; font-size: 0.9rem; margin-top: 0.5rem;">15 minutes ago</div>
                            </div>
                            <div style="border-left: 4px solid #dc3545; padding: 1rem; margin-bottom: 1rem; background: #f8f9fa; border-radius: 5px;">
                                <strong>Fraud Alert:</strong> Suspicious claim pattern detected - sent to investigation
                                <div style="color: #666; font-size: 0.9rem; margin-top: 0.5rem;">1 hour ago</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1);">
                        <h3 style="color: #1e3c72; margin-bottom: 1rem;">AI Agent Status</h3>
                        <div style="space-y: 1rem;">
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.8rem; background: #d4edda; border-radius: 8px; margin-bottom: 0.8rem;">
                                <span>Document Processor</span>
                                <span style="color: #28a745; font-weight: bold;">●</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.8rem; background: #d4edda; border-radius: 8px; margin-bottom: 0.8rem;">
                                <span>Risk Analyzer</span>
                                <span style="color: #28a745; font-weight: bold;">●</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.8rem; background: #d4edda; border-radius: 8px; margin-bottom: 0.8rem;">
                                <span>Fraud Detection</span>
                                <span style="color: #28a745; font-weight: bold;">●</span>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.8rem; background: #fff3cd; border-radius: 8px; margin-bottom: 0.8rem;">
                                <span>Claims Triage</span>
                                <span style="color: #ffc107; font-weight: bold;">●</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Submissions Page -->
            <div id="submissions" class="page">
                <h1 style="color: #1e3c72; margin-bottom: 2rem;">Submission Processing</h1>
                
                <div class="processing-layout">
                    <div class="panel">
                        <div class="panel-header">📄 Uploaded Submission</div>
                        <div class="pdf-viewer">
                            <div style="text-align: center; color: #666;">
                                <div style="font-size: 3rem; margin-bottom: 1rem;">📄</div>
                                <div>Commercial Property Application</div>
                                <div style="font-size: 0.9rem; margin-top: 0.5rem;">ABC_Corp_Property_App.pdf</div>
                            </div>
                        </div>
                        
                        <div style="background: white; padding: 1rem; border-radius: 10px; font-family: monospace; font-size: 0.9rem; line-height: 1.6;">
                            <strong>COMMERCIAL PROPERTY APPLICATION</strong><br><br>
                            
                            <div class="highlight-box">
                                <strong>Applicant:</strong> John Doe<br>
                                <strong>Company:</strong> ABC Manufacturing Corp
                            </div>
                            
                            Property Address: 123 Industrial Blvd, Chicago, IL<br>
                            Building Type: Manufacturing Facility<br>
                            
                            <div class="highlight-box">
                                <strong>Coverage Requested:</strong> $500,000 Property Damage<br>
                                <strong>Deductible:</strong> $10,000
                            </div>
                            
                            Year Built: 1998<br>
                            Square Footage: 50,000 sq ft<br>
                            
                            <div class="highlight-box">
                                <strong>Premium:</strong> $2,100 annually
                            </div>
                            
                            Fire Protection: Sprinkler system installed 2020<br>
                            Security: 24/7 surveillance system<br>
                            Prior Claims: None in last 5 years
                        </div>
                    </div>
                    
                    <div class="panel">
                        <div class="panel-header">🤖 AI Agent Analysis</div>
                        <div style="background: rgba(78, 205, 196, 0.1); padding: 1rem; border-radius: 10px; margin-bottom: 1rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                                <div style="width: 8px; height: 8px; border-radius: 50%; background: #4ecdc4; animation: pulse 2s infinite;"></div>
                                <span style="font-weight: bold;">Processing Complete</span>
                                <span class="model-source-indicator private">🔒 Private AI Hosting</span>
                            </div>
                            <div style="font-size: 0.85rem; color: #666;">Hybrid GenAI + AI Models — cost-efficient, privately hosted. No third-party data sharing.</div>
                        </div>
                        
                        <!-- Agent Reasoning Steps -->
                        <div class="reasoning-steps">
                            <h4 style="color: #1e3c72; margin-bottom: 1rem;">🧠 Agent Reasoning Steps</h4>
                            
                            <div class="reasoning-step">
                                <div class="reasoning-icon">1</div>
                                <div>
                                    <strong>Document Classification:</strong><br>
                                    <span style="color: #666;">Identified as Commercial Property Application with 99.7% confidence based on form structure and content patterns.</span>
                                </div>
                            </div>
                            
                            <div class="reasoning-step">
                                <div class="reasoning-icon">2</div>
                                <div>
                                    <strong>Risk Factor Analysis:</strong><br>
                                    <span style="color: #666;">Evaluated building age (-5 pts), fire protection (+15 pts), security systems (+10 pts), location risk (-3 pts), clean history (+12 pts).</span>
                                </div>
                            </div>
                            
                            <div class="reasoning-step">
                                <div class="reasoning-icon">3</div>
                                <div>
                                    <strong>Bias Verification:</strong><br>
                                    <span style="color: #666;">Confirmed no protected attributes influenced decision. Geographic and demographic factors within fair ranges.</span>
                                </div>
                            </div>
                            
                            <div class="reasoning-step">
                                <div class="reasoning-icon">4</div>
                                <div>
                                    <strong>Business Rules Check:</strong><br>
                                    <span style="color: #666;">All 12 business rules passed. Coverage within limits, premium calculations verified, compliance requirements met.</span>
                                </div>
                            </div>
                        </div>
                        
                        <div style="margin-bottom: 1.5rem;">
                            <h4 style="color: #1e3c72; margin-bottom: 1rem;">Highlighted Fields:</h4>
                            <div style="background: rgba(255, 235, 59, 0.2); padding: 0.5rem; border-radius: 5px; margin-bottom: 0.5rem;">
                                ✓ Policyholder identified
                            </div>
                            <div style="background: rgba(255, 235, 59, 0.2); padding: 0.5rem; border-radius: 5px; margin-bottom: 0.5rem;">
                                ✓ Coverage amount extracted
                            </div>
                            <div style="background: rgba(255, 235, 59, 0.2); padding: 0.5rem; border-radius: 5px; margin-bottom: 0.5rem;">
                                ✓ Premium calculated
                            </div>
                        </div>
                        
                        <div style="background: white; padding: 1rem; border-radius: 10px;">
                            <h4 style="color: #1e3c72; margin-bottom: 1rem;">Risk Factors Analyzed:</h4>
                            <ul style="color: #666; line-height: 1.6;">
                                <li>✅ Building age (acceptable)</li>
                                <li>✅ Fire protection (excellent)</li>
                                <li>✅ Security measures (good)</li>
                                <li>✅ Claims history (clean)</li>
                                <li>⚠️ Location (industrial area - moderate risk)</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="panel">
                        <div class="panel-header">📊 Extracted Data</div>
                        <table class="data-table">
                            <tr>
                                <td>Policyholder:</td>
                                <td>John Doe</td>
                            </tr>
                            <tr>
                                <td>Company:</td>
                                <td>ABC Manufacturing Corp</td>
                            </tr>
                            <tr>
                                <td>Coverage:</td>
                                <td>$500,000 Property Damage</td>
                            </tr>
                            <tr>
                                <td>Deductible:</td>
                                <td>$10,000</td>
                            </tr>
                            <tr>
                                <td>Premium:</td>
                                <td>$2,100 annually</td>
                            </tr>
                            <tr>
                                <td>Building Type:</td>
                                <td>Manufacturing Facility</td>
                            </tr>
                            <tr>
                                <td>Square Footage:</td>
                                <td>50,000 sq ft</td>
                            </tr>
                            <tr>
                                <td>Year Built:</td>
                                <td>1998</td>
                            </tr>
                        </table>
                        
                        <div class="agent-decision">
                            <div class="decision-header">🤖 Agent Decision</div>
                            <div class="risk-score">
                                <div class="score-circle">87</div>
                                <div>
                                    <div><strong>Risk Score: Low</strong></div>
                                    <div style="color: #666; font-size: 0.9rem;">Score: 87/100</div>
                                </div>
                            </div>
                            
                            <!-- Bias Check Section -->
                            <div style="background: rgba(220, 248, 198, 0.3); border-left: 4px solid #28a745; padding: 1rem; margin: 1rem 0; border-radius: 5px;">
                                <div style="font-weight: bold; color: #155724; margin-bottom: 0.5rem;">⚖️ Bias Check: PASSED</div>
                                <div style="font-size: 0.85rem; color: #155724;">
                                    • No protected class discrimination detected<br>
                                    • Geographic fairness verified<br>
                                    • Age/gender neutral assessment<br>
                                    • Bias score: 0.08 (acceptable < 0.2)
                                </div>
                            </div>
                            
                            <div style="margin-top: 1rem;">
                                <div style="color: #155724;"><strong>✓ Recommendation: Auto-bind</strong></div>
                                <div style="color: #666; font-size: 0.9rem; margin-top: 0.5rem;">
                                    All risk factors within acceptable parameters. No manual review required.
                                </div>
                            </div>
                            <button class="btn btn-approve" style="width: 100%; margin-top: 1rem;" onclick="processSubmission()">
                                Approve & Bind Policy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Claims Page -->
            <div id="claims" class="page">
                <h1 style="color: #1e3c72; margin-bottom: 2rem;">Claims Triage</h1>
                
                <div class="triage-layout">
                    <div class="claim-document">
                        <h3 style="color: #1e3c72; margin-bottom: 1rem;">Claim Document Analysis</h3>
                        <div class="document-content">
                            <p><strong>CLAIM REPORT #CLM-2025-0892</strong></p>
                            <p><strong>Date of Loss:</strong> September 15, 2025</p>
                            <p><strong>Policyholder:</strong> Sarah Johnson</p>
                            <p><strong>Policy Number:</strong> POL-789456</p>
                            
                            <p style="margin-top: 1.5rem;"><strong>Incident Description:</strong></p>
                            <p>On September 15th at approximately <span class="highlighted-text">2:30 PM</span>, I was driving southbound on Main Street when another vehicle ran a red light and <span class="highlighted-text">collided with the passenger side of my 2022 Honda Civic</span>. The other driver appeared to be <span class="highlighted-text">using their mobile phone</span> at the time of impact.</p>
                            
                            <p style="margin-top: 1rem;"><strong>Damage Assessment:</strong></p>
                            <p>Significant damage to <span class="highlighted-text">passenger side door, rear quarter panel, and side mirror</span>. Vehicle is drivable but requires immediate repair. No injuries reported at scene.</p>
                            
                            <p style="margin-top: 1rem;"><strong>Police Report:</strong> Filed, Report #PR-2025-3847</p>
                            <p><strong>Estimated Repair Cost:</strong> <span class="highlighted-text">$8,500</span></p>
                            <p><strong>Witness Information:</strong> Available upon request</p>
                        </div>
                    </div>
                    
                    <div class="agent-analysis">
                        <h3 style="color: #1e3c72; margin-bottom: 1rem;">AI Agent Output</h3>
                        
                        <div class="analysis-item">
                            <div class="analysis-label">Claim Type</div>
                            <div class="analysis-value">🚗 Auto Accident</div>
                        </div>
                        
                        <div class="analysis-item">
                            <div class="analysis-label">Estimated Settlement</div>
                            <div class="analysis-value">💰 $8,500</div>
                        </div>
                        
                        <div class="analysis-item">
                            <div class="analysis-label">Fraud Risk Assessment</div>
                            <div class="analysis-value">
                                <span style="color: #ffc107; font-weight: bold;">⚠️ Medium Risk</span>
                                <div style="font-size: 0.9rem; color: #666; margin-top: 0.5rem;">
                                    No immediate red flags, but recommend verification of witness statements
                                </div>
                            </div>
                        </div>
                        
                        <!-- Fraud Risk Explanation -->
                        <div class="analysis-item">
                            <div class="analysis-label">🕵️ Fraud Detection Analysis</div>
                            <div class="analysis-value">
                                <div style="background: #fff3cd; padding: 1rem; border-radius: 8px; margin-top: 0.5rem;">
                                    <div style="font-weight: bold; color: #856404; margin-bottom: 0.5rem;">Anomalies Detected:</div>
                                    <div class="anomaly-highlight">
                                        Repair estimate higher than typical for damage described
                                        <div class="anomaly-indicator">!</div>
                                    </div>
                                    <div style="margin-top: 0.5rem; font-size: 0.85rem; color: #856404;">
                                        <strong>Explanation:</strong> $8,500 estimate is 15% above average for similar side-impact damage. However, vehicle age and repair shop location justify premium pricing.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="analysis-item">
                            <div class="analysis-label">Key Factors Identified</div>
                            <div class="analysis-value">
                                <ul style="font-size: 0.9rem; color: #666; margin-top: 0.5rem; padding-left: 1rem;">
                                    <li>Other party fault (red light violation)</li>
                                    <li>Police report filed</li>
                                    <li>No injuries claimed</li>
                                    <li>Reasonable repair estimate</li>
                                    <li>Witnesses available</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="analysis-item">
                            <div class="analysis-label">Recommended Action</div>
                            <div class="analysis-value" style="color: #28a745; font-weight: bold;">
                                ✓ Approve with Standard Processing
                            </div>
                        </div>
                        
                        <div class="analysis-item">
                            <div class="analysis-label">Bias & Fairness Check</div>
                            <div class="analysis-value">
                                <div style="color: #28a745; font-weight: bold;">✅ PASSED</div>
                                <div style="font-size: 0.85rem; color: #666; margin-top: 0.5rem;">
                                    • No demographic bias detected<br>
                                    • Settlement amount within fair range<br>
                                    • Decision factors are risk-based only<br>
                                    • Bias score: 0.06 (excellent)
                                </div>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <button class="btn btn-approve" onclick="approveClaim()">Approve</button>
                            <button class="btn btn-review" onclick="sendToAdjuster()">Send to Adjuster</button>
                            <button class="btn btn-reject" onclick="rejectClaim()">Reject</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Audit Page -->
            <div id="audit" class="page">
                <h1 style="color: #1e3c72; margin-bottom: 2rem;">Audit & Compliance Panel</h1>
                
                <div style="background: rgba(78, 205, 196, 0.1); padding: 1.5rem; border-radius: 15px; margin-bottom: 2rem;">
                    <h3 style="color: #1e3c72; margin-bottom: 1rem;">Current Processing: Submission #SUB-2025-1247</h3>
                    <div style="color: #666;">Real-time audit trail for ABC Manufacturing Corp property insurance application</div>
                </div>
                
                <div class="timeline">
                    <div class="timeline-item" onclick="showStepDetails(1)">
                        <div class="timeline-marker completed"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">Step 1: Document Ingested</div>
                            <div class="timeline-description">Agent A - Document Processing Agent</div>
                            <div class="timeline-details">
                                <strong>Timestamp:</strong> 2025-09-27 14:23:15<br>
                                <strong>Processing Time:</strong> 0.8 seconds<br>
                                <strong>Status:</strong> ✅ Complete<br>
                                <strong>Output:</strong> Document classified as Commercial Property Application, 8 pages processed
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item" onclick="showStepDetails(2)">
                        <div class="timeline-marker completed"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">Step 2: Data Extracted</div>
                            <div class="timeline-description">Agent B - Data Extraction Agent</div>
                            <div class="timeline-details">
                                <strong>Timestamp:</strong> 2025-09-27 14:23:17<br>
                                <strong>Processing Time:</strong> 2.3 seconds<br>
                                <strong>Status:</strong> ✅ Complete<br>
                                <strong>Confidence Score:</strong> 94%<br>
                                <strong>Fields Extracted:</strong> 23/25 required fields populated
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item" onclick="showStepDetails(3)">
                        <div class="timeline-marker completed"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">Step 3: Risk Score Generated</div>
                            <div class="timeline-description">Agent C - Risk Assessment Agent</div>
                            <div class="timeline-details">
                                <strong>Timestamp:</strong> 2025-09-27 14:23:21<br>
                                <strong>Processing Time:</strong> 4.1 seconds<br>
                                <strong>Status:</strong> ✅ Complete<br>
                                <strong>Risk Score:</strong> 87/100 (Low Risk)<br>
                                <strong>Factors Analyzed:</strong> 15 risk categories evaluated
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item" onclick="showStepDetails(4)">
                        <div class="timeline-marker current"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">Step 4: Decision Suggested</div>
                            <div class="timeline-description">Agent D - Decision Engine</div>
                            <div class="timeline-details">
                                <strong>Timestamp:</strong> 2025-09-27 14:23:26<br>
                                <strong>Processing Time:</strong> 1.2 seconds<br>
                                <strong>Status:</strong> ✅ Complete<br>
                                <strong>Recommendation:</strong> Auto-bind (High Confidence: 97%)<br>
                                <strong>Business Rules Applied:</strong> 12/12 passed
                            </div>
                        </div>
                    </div>
                    
                    <div class="timeline-item" onclick="showStepDetails(5)">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <div class="timeline-title">Step 5: Human in Loop</div>
                            <div class="timeline-description">Underwriter Review (if required)</div>
                            <div class="timeline-details">
                                <strong>Status:</strong> ⏳ Bypassed - Auto-approval threshold met<br>
                                <strong>Assigned Underwriter:</strong> System Override<br>
                                <strong>Review Required:</strong> No - Risk score within auto-bind parameters
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="background: white; padding: 2rem; border-radius: 15px; box-shadow: 0 5px 20px rgba(0,0,0,0.1); margin-top: 2rem;">
                    <h3 style="color: #1e3c72; margin-bottom: 1rem;">Audit Summary</h3>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem;">
                        <div style="text-align: center; padding: 1rem; background: #d4edda; border-radius: 10px;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #28a745;">8.7s</div>
                            <div style="color: #666;">Total Processing Time</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #d1ecf1; border-radius: 10px;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #0c5460;">4</div>
                            <div style="color: #666;">AI Agents Involved</div>
                        </div>
                        <div style="text-align: center; padding: 1rem; background: #fff3cd; border-radius: 10px;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #856404;">100%</div>
                            <div style="color: #666;">Compliance Score</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Governance & Fairness Page -->
            <div id="governance" class="page">
                <h1 style="color: #1e3c72; margin-bottom: 2rem;">Governance & Fairness Oversight</h1>
                
                <!-- Fairness Dashboard -->
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 1.5rem; margin-bottom: 2rem;">
                    <div style="background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%); color: white; padding: 1.5rem; border-radius: 15px; text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold;">98.2%</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">Overall Fairness Score</div>
                        <div style="font-size: 0.8rem; margin-top: 0.5rem;">Industry benchmark: 94%</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 1.5rem; border-radius: 15px; text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold;">0.12</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">Global Bias Index</div>
                        <div style="font-size: 0.8rem; margin-top: 0.5rem;">Target: < 0.20</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 1.5rem; border-radius: 15px; text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold;">3</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">Bias Alerts (30 days)</div>
                        <div style="font-size: 0.8rem; margin-top: 0.5rem;">All resolved</div>
                    </div>
                    <div style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333; padding: 1.5rem; border-radius: 15px; text-align: center;">
                        <div style="font-size: 2rem; font-weight: bold;">847</div>
                        <div style="font-size: 0.9rem;">Decisions Audited</div>
                        <div style="font-size: 0.8rem; margin-top: 0.5rem;">This week</div>
                    </div>
                </div>
                
                <!-- Protected Classes Monitoring -->
                <div style="background: white; border-radius: 20px; padding: 2rem; box-shadow: 0 10px 40px rgba(0,0,0,0.1); margin-bottom: 2rem;">
                    <h2 style="color: #1e3c72; margin-bottom: 1.5rem;">Protected Classes Monitoring</h2>
                    
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 2rem;">
                        <div>
                            <h4 style="color: #333; margin-bottom: 1rem;">Age Demographics</h4>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 10px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>18-30 years</span>
                                    <span style="font-weight: bold; color: #28a745;">97.8%</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>31-50 years</span>
                                    <span style="font-weight: bold; color: #28a745;">98.1%</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>51+ years</span>
                                    <span style="font-weight: bold; color: #28a745;">98.4%</span>
                                </div>
                                <div style="margin-top: 1rem; padding: 0.5rem; background: #d4edda; border-radius: 5px; font-size: 0.85rem; color: #155724;">
                                    ✅ No age-based bias detected. Approval rates consistent across all age groups.
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h4 style="color: #333; margin-bottom: 1rem;">Geographic Fairness</h4>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 10px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>Urban areas</span>
                                    <span style="font-weight: bold; color: #28a745;">97.9%</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>Suburban areas</span>
                                    <span style="font-weight: bold; color: #28a745;">98.3%</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>Rural areas</span>
                                    <span style="font-weight: bold; color: #ffc107;">96.8%</span>
                                </div>
                                <div style="margin-top: 1rem; padding: 0.5rem; background: #fff3cd; border-radius: 5px; font-size: 0.85rem; color: #856404;">
                                    ⚠️ Minor variance in rural approval rates. Within acceptable range but monitored.
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h4 style="color: #333; margin-bottom: 1rem;">Business Types</h4>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 10px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>Manufacturing</span>
                                    <span style="font-weight: bold; color: #28a745;">97.5%</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>Retail/Services</span>
                                    <span style="font-weight: bold; color: #28a745;">98.7%</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span>Technology</span>
                                    <span style="font-weight: bold; color: #28a745;">98.9%</span>
                                </div>
                                <div style="margin-top: 1rem; padding: 0.5rem; background: #d4edda; border-radius: 5px; font-size: 0.85rem; color: #155724;">
                                    ✅ Fair treatment across business types. Differences reflect legitimate risk factors.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Bias Detection Alerts -->
                <div style="background: white; border-radius: 20px; padding: 2rem; box-shadow: 0 10px 40px rgba(0,0,0,0.1); margin-bottom: 2rem;">
                    <h2 style="color: #1e3c72; margin-bottom: 1.5rem;">Recent Bias Alerts & Resolutions</h2>
                    
                    <div style="margin-bottom: 1.5rem;">
                        <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 1rem; border-radius: 5px; margin-bottom: 1rem;">
                            <div style="display: flex; justify-content: between; align-items: start;">
                                <div style="flex: 1;">
                                    <div style="font-weight: bold; color: #856404;">⚠️ Bias Alert #BA-2025-0023</div>
                                    <div style="color: #856404; margin: 0.5rem 0;">
                                        <strong>Issue:</strong> Geographic cluster showing 3% higher rejection rate
                                    </div>
                                    <div style="color: #666; font-size: 0.9rem;">
                                        <strong>Location:</strong> Detroit Metro Area (Commercial Auto)<br>
                                        <strong>Detected:</strong> Sep 24, 2025 at 14:32<br>
                                        <strong>Status:</strong> RESOLVED - Investigation revealed legitimate risk correlation with local road conditions
                                    </div>
                                </div>
                                <div style="background: #28a745; color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem; margin-left: 1rem;">
                                    RESOLVED
                                </div>
                            </div>
                        </div>
                        
                        <div style="background: #d1ecf1; border-left: 4px solid #17a2b8; padding: 1rem; border-radius: 5px; margin-bottom: 1rem;">
                            <div style="display: flex; justify-content: between; align-items: start;">
                                <div style="flex: 1;">
                                    <div style="font-weight: bold; color: #0c5460;">ℹ️ Routine Audit #RA-2025-0156</div>
                                    <div style="color: #0c5460; margin: 0.5rem 0;">
                                        <strong>Scope:</strong> Monthly fairness review across all decision agents
                                    </div>
                                    <div style="color: #666; font-size: 0.9rem;">
                                        <strong>Period:</strong> August 1-31, 2025<br>
                                        <strong>Decisions Reviewed:</strong> 3,247<br>
                                        <strong>Result:</strong> No significant bias patterns detected. All metrics within acceptable thresholds.
                                    </div>
                                </div>
                                <div style="background: #28a745; color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem; margin-left: 1rem;">
                                    PASSED
                                </div>
                            </div>
                        </div>
                        
                        <div style="background: #f8d7da; border-left: 4px solid #dc3545; padding: 1rem; border-radius: 5px; margin-bottom: 1rem;">
                            <div style="display: flex; justify-content: between; align-items: start;">
                                <div style="flex: 1;">
                                    <div style="font-weight: bold; color: #721c24;">🚨 Critical Alert #CA-2025-0008</div>
                                    <div style="color: #721c24; margin: 0.5rem 0;">
                                        <strong>Issue:</strong> Potential age discrimination in premium calculations
                                    </div>
                                    <div style="color: #666; font-size: 0.9rem;">
                                        <strong>Detected:</strong> Sep 20, 2025 at 09:15<br>
                                        <strong>Action Taken:</strong> Immediate agent suspension, manual review activated<br>
                                        <strong>Resolution:</strong> False positive - age correlation was due to building vintage risk factor
                                    </div>
                                </div>
                                <div style="background: #28a745; color: white; padding: 0.3rem 0.8rem; border-radius: 15px; font-size: 0.8rem; margin-left: 1rem;">
                                    RESOLVED
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Governance Controls -->
                <div style="background: white; border-radius: 20px; padding: 2rem; box-shadow: 0 10px 40px rgba(0,0,0,0.1); margin-bottom: 2rem;">
                    <h2 style="color: #1e3c72; margin-bottom: 1.5rem;">Governance Controls & Policies</h2>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                        <div>
                            <h4 style="color: #333; margin-bottom: 1rem;">Bias Detection Thresholds</h4>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 10px;">
                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Global Bias Threshold:</label>
                                    <input type="range" min="0.1" max="0.5" step="0.05" value="0.2" style="width: 100%;">
                                    <div style="text-align: center; color: #666; margin-top: 0.5rem;">0.20 (Current: 0.12)</div>
                                </div>
                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Alert Sensitivity:</label>
                                    <select style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #ddd;">
                                        <option>Low</option>
                                        <option selected>Medium</option>
                                        <option>High</option>
                                        <option>Maximum</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Auto-Suspension Trigger:</label>
                                    <input type="checkbox" checked> Enable automatic agent suspension on critical bias alerts
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h4 style="color: #333; margin-bottom: 1rem;">Audit Frequency</h4>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 10px;">
                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Routine Audits:</label>
                                    <select style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #ddd;">
                                        <option>Weekly</option>
                                        <option selected>Monthly</option>
                                        <option>Quarterly</option>
                                    </select>
                                </div>
                                <div style="margin-bottom: 1rem;">
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Sample Size:</label>
                                    <input type="number" value="1000" min="100" max="10000" style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #ddd;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Real-time Monitoring:</label>
                                    <input type="checkbox" checked> Enable continuous bias monitoring
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Explainability & Transparency -->
                <div style="background: white; border-radius: 20px; padding: 2rem; box-shadow: 0 10px 40px rgba(0,0,0,0.1); margin-bottom: 2rem;">
                    <h2 style="color: #1e3c72; margin-bottom: 1.5rem;">AI Explainability & Transparency</h2>
                    
                    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 2rem;">
                        <div>
                            <h4 style="color: #333; margin-bottom: 1rem;">Decision Factors Transparency</h4>
                            <div style="background: #f8f9fa; padding: 1.5rem; border-radius: 10px;">
                                <div style="margin-bottom: 1rem;">
                                    <div style="font-weight: bold; margin-bottom: 0.5rem;">Sample Decision Breakdown:</div>
                                    <div style="background: white; padding: 1rem; border-radius: 8px; border-left: 4px solid #28a745;">
                                        <div style="font-weight: bold; color: #1e3c72; margin-bottom: 0.5rem;">Policy #POL-2025-8742 - APPROVED</div>
                                        <div style="font-size: 0.9rem; color: #666; line-height: 1.6;">
                                            <strong>Top Decision Factors:</strong><br>
                                            • Building fire protection system: +15 points (42% weight)<br>
                                            • Clean claims history: +12 points (28% weight)<br>
                                            • Industry risk category: +8 points (18% weight)<br>
                                            • Property age factor: -5 points (12% weight)<br><br>
                                            <strong>Protected Attributes Excluded:</strong><br>
                                            ❌ Business owner age, gender, ethnicity<br>
                                            ❌ Neighborhood demographics<br>
                                            ❌ Personal characteristics<br><br>
                                            <strong>Bias Check:</strong> ✅ No discriminatory patterns detected
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h4 style="color: #333; margin-bottom: 1rem;">Transparency Metrics</h4>
                            <div style="background: #f8f9fa; padding: 1rem; border-radius: 10px;">
                                <div style="text-align: center; padding: 1rem; background: #d4edda; border-radius: 8px; margin-bottom: 1rem;">
                                    <div style="font-size: 1.5rem; font-weight: bold; color: #28a745;">94%</div>
                                    <div style="font-size: 0.9rem; color: #155724;">Decision Explainability Score</div>
                                </div>
                                <div style="text-align: center; padding: 1rem; background: #d1ecf1; border-radius: 8px; margin-bottom: 1rem;">
                                    <div style="font-size: 1.5rem; font-weight: bold; color: #0c5460;">847</div>
                                    <div style="font-size: 0.9rem; color: #0c5460;">Decisions with Full Audit Trail</div>
                                </div>
                                <div style="text-align: center; padding: 1rem; background: #fff3cd; border-radius: 8px;">
                                    <div style="font-size: 1.5rem; font-weight: bold; color: #856404;">0</div>
                                    <div style="font-size: 0.9rem; color: #856404;">"Black Box" Decisions</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                    <button class="btn" onclick="generateBiasReport()" style="flex: 1;">📊 Generate Bias Report</button>
                    <button class="btn" onclick="runFairnessAudit()" style="flex: 1; background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);">🔍 Run Fairness Audit</button>
                    <button class="btn" onclick="exportGovernanceData()" style="flex: 1; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">📤 Export Governance Data</button>
                </div>
            </div>
            
            <!-- Admin Page -->
            <div id="admin" class="page">
                <h1 style="color: #1e3c72; margin-bottom: 2rem;">Admin - Agent Orchestration & Marketplace</h1>
                
                <div class="agent-toolbox">
                    <div class="toolbox-title">🧰 Available Agents</div>
                    <div class="toolbox-agents">
                        <div class="toolbox-agent" draggable="true">📄 Document Intake Agent</div>
                        <div class="toolbox-agent" draggable="true">🔍 Data Extraction Agent</div>
                        <div class="toolbox-agent" draggable="true">⚠️ Risk Scoring Agent</div>
                        <div class="toolbox-agent" draggable="true">🕵️ Fraud Detection Agent</div>
                        <div class="toolbox-agent" draggable="true">💰 Quote Agent</div>
                        <div class="toolbox-agent" draggable="true">📊 Analytics Agent</div>
                        <div class="toolbox-agent" draggable="true">✉️ Communication Agent</div>
                    </div>
                </div>
                
                <div class="workflow-canvas">
                    <h3 style="color: #1e3c72; margin-bottom: 2rem;">Current Workflow Configuration</h3>
                    
                    <div style="display: flex; align-items: center; flex-wrap: wrap; gap: 1rem;">
                        <div class="agent-node">
                            📄 Document Intake Agent
                            <div style="font-size: 0.8rem; margin-top: 0.5rem; opacity: 0.9;">Processes incoming docs</div>
                        </div>
                        
                        <div class="workflow-arrow">→</div>
                        
                        <div class="agent-node">
                            🔍 Data Extraction Agent
                            <div style="font-size: 0.8rem; margin-top: 0.5rem; opacity: 0.9;">Extracts structured data</div>
                        </div>
                        
                        <div class="workflow-arrow">→</div>
                        
                        <div class="agent-node">
                            ⚠️ Risk Scoring Agent
                            <div style="font-size: 0.8rem; margin-top: 0.5rem; opacity: 0.9;">Calculates risk scores</div>
                        </div>
                        
                        <div class="workflow-arrow">→</div>
                        
                        <div class="agent-node">
                            🕵️ Fraud Detection Agent
                            <div style="font-size: 0.8rem; margin-top: 0.5rem; opacity: 0.9;">Detects anomalies</div>
                        </div>
                        
                        <div class="workflow-arrow">→</div>
                        
                        <div class="agent-node decision">
                            🤔 Decision Engine
                            <div style="font-size: 0.8rem; margin-top: 0.5rem; opacity: 0.9;">Auto-bind logic</div>
                        </div>
                        
                        <div class="workflow-arrow">→</div>
                        
                        <div class="agent-node human">
                            👤 Human Approval
                            <div style="font-size: 0.8rem; margin-top: 0.5rem; opacity: 0.9;">Manual review if needed</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 3rem; padding: 1.5rem; background: rgba(102, 126, 234, 0.1); border-radius: 15px;">
                        <h4 style="color: #1e3c72; margin-bottom: 1rem;">Workflow Settings</h4>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 2rem;">
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Auto-bind Threshold:</label>
                                <input type="range" min="70" max="95" value="85" style="width: 100%;">
                                <div style="text-align: center; color: #666; margin-top: 0.5rem;">85% confidence</div>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 0.5rem; font-weight: bold;">Fraud Alert Sensitivity:</label>
                                <select style="width: 100%; padding: 0.5rem; border-radius: 5px; border: 1px solid #ddd;">
                                    <option>Low</option>
                                    <option selected>Medium</option>
                                    <option>High</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Agent Marketplace -->
                <div style="background: white; border-radius: 20px; padding: 2rem; box-shadow: 0 10px 40px rgba(0,0,0,0.1); margin: 2rem 0;">
                    <h2 style="color: #1e3c72; margin-bottom: 1.5rem;">🛒 Agent Marketplace - Plug & Play Intelligence</h2>

                    <div class="agent-marketplace">
                        <div class="marketplace-agent installed">
                            <div class="agent-icon" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white;">📄</div>
                            <h4>Document Processor Pro</h4>
                            <div class="agent-rating">⭐⭐⭐⭐⭐ 4.9</div>
                            <p style="font-size: 0.85rem; color: #666;">Advanced OCR with 99.8% accuracy</p>
                            <div style="margin-top: 1rem; color: #28a745; font-weight: bold;">✅ INSTALLED</div>
                        </div>

                        <div class="marketplace-agent installed">
                            <div class="agent-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">🕵️</div>
                            <h4>Fraud Detection AI</h4>
                            <div class="agent-rating">⭐⭐⭐⭐⭐ 4.8</div>
                            <p style="font-size: 0.85rem; color: #666;">ML-powered anomaly detection</p>
                            <div style="margin-top: 1rem; color: #28a745; font-weight: bold;">✅ INSTALLED</div>
                        </div>

                        <div class="marketplace-agent">
                            <div class="agent-icon" style="background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%); color: #333;">🤖</div>
                            <h4>Premium Calculator Plus</h4>
                            <div class="agent-rating">⭐⭐⭐⭐☆ 4.6</div>
                            <p style="font-size: 0.85rem; color: #666;">Advanced pricing algorithms</p>
                            <div style="margin-top: 1rem;">
                                <button class="btn" style="background: #4ecdc4; padding: 0.5rem 1rem; font-size: 0.85rem;" onclick="installAgent('premium')">Install Agent</button>
                            </div>
                        </div>

                        <div class="marketplace-agent">
                            <div class="agent-icon" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); color: #333;">🌐</div>
                            <h4>Multi-Language Processor</h4>
                            <div class="agent-rating">⭐⭐⭐⭐⭐ 4.9</div>
                            <p style="font-size: 0.85rem; color: #666;">50+ language support</p>
                            <div style="margin-top: 1rem;">
                                <button class="btn" style="background: #4ecdc4; padding: 0.5rem 1rem; font-size: 0.85rem;" onclick="installAgent('language')">Install Agent</button>
                            </div>
                        </div>

                        <div class="marketplace-agent">
                            <div class="agent-icon" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;">📊</div>
                            <h4>Predictive Analytics</h4>
                            <div class="agent-rating">⭐⭐⭐⭐☆ 4.7</div>
                            <p style="font-size: 0.85rem; color: #666;">Future risk prediction</p>
                            <div style="margin-top: 1rem;">
                                <button class="btn" style="background: #4ecdc4; padding: 0.5rem 1rem; font-size: 0.85rem;" onclick="installAgent('analytics')">Install Agent</button>
                            </div>
                        </div>

                        <div class="marketplace-agent">
                            <div class="agent-icon" style="background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%); color: white;">⚖️</div>
                            <h4>Compliance Guardian</h4>
                            <div class="agent-rating">⭐⭐⭐⭐⭐ 4.8</div>
                            <p style="font-size: 0.85rem; color: #666;">Real-time regulatory compliance</p>
                            <div style="margin-top: 1rem;">
                                <button class="btn" style="background: #4ecdc4; padding: 0.5rem 1rem; font-size: 0.85rem;" onclick="installAgent('compliance')">Install Agent</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="display: flex; gap: 1rem; margin-top: 2rem;">
                    <button class="btn" style="flex: 1;" onclick="saveWorkflow()">💾 Save Workflow</button>
                    <button class="btn" style="flex: 1; background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); color: #333;" onclick="testWorkflow()">🧪 Test Workflow</button>
                    <button class="btn" style="flex: 1; background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); color: #333;" onclick="resetWorkflow()">🔄 Reset to Default</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal for detailed step information -->
    <div id="stepModal" class="modal">
        <div class="modal-content">
            <button class="modal-close" onclick="closeModal()">&times;</button>
            <div id="modalContent"></div>
        </div>
    </div>
    
    <script>
        // Login functionality
        function login(event) {
            event.preventDefault();
            document.getElementById('loginPage').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';
        }
        
        function logout() {
            document.getElementById('mainApp').style.display = 'none';
            document.getElementById('loginPage').style.display = 'block';
        }
        
        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // Remove active class from all nav buttons
            document.querySelectorAll('.nav-links button').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Show selected page and activate button
            document.getElementById(pageId).classList.add('active');
            event.target.classList.add('active');
        }
        
        // Submission processing
        function processSubmission() {
            alert('✅ Policy approved and bound successfully!\n\nPolicy Number: POL-2025-8742\nPremium: $2,100 annually\nEffective Date: Today\n\nConfirmation email sent to policyholder.');
        }
        
        // Claims functions
        function approveClaim() {
            alert('✅ Claim approved!\n\nClaim Number: CLM-2025-0892\nApproved Amount: $8,500\nProcessing Time: 3.2 minutes\n\nPayment will be processed within 24 hours.');
        }
        
        function sendToAdjuster() {
            alert('📋 Claim sent to adjuster for manual review.\n\nAssigned to: Senior Adjuster Mike Thompson\nExpected Review Time: 2-3 business days\n\nNotification sent to all parties.');
        }
        
        function rejectClaim() {
            alert('❌ Claim rejected.\n\nReason codes will be generated and sent to policyholder.\nAppeal process information included.');
        }
        
        // Audit timeline details
        function showStepDetails(stepNumber) {
            const stepDetails = {
                1: {
                    title: "Document Ingestion - Agent A",
                    details: `
                        <h3>Document Processing Agent</h3>
                        <p><strong>Agent ID:</strong> DOC-PROC-001</p>
                        <p><strong>Processing Start:</strong> 2025-09-27 14:23:15.123</p>
                        <p><strong>Processing End:</strong> 2025-09-27 14:23:15.945</p>
                        <p><strong>Total Time:</strong> 0.822 seconds</p>
                        
                        <h4>Processing Steps:</h4>
                        <ul>
                            <li>✅ Document format validation (PDF, 8 pages)</li>
                            <li>✅ OCR processing with 99.7% accuracy</li>
                            <li>✅ Document classification: Commercial Property Application</li>
                            <li>✅ Quality assessment: High confidence</li>
                        </ul>
                        
                        <h4>Output Generated:</h4>
                        <p>Structured document object with 847 extracted text elements, 23 form fields identified, and metadata classification complete.</p>
                    `
                },
                2: {
                    title: "Data Extraction - Agent B",
                    details: `
                        <h3>Data Extraction Agent</h3>
                        <p><strong>Agent ID:</strong> DATA-EXT-002</p>
                        <p><strong>Processing Start:</strong> 2025-09-27 14:23:16.001</p>
                        <p><strong>Processing End:</strong> 2025-09-27 14:23:18.334</p>
                        <p><strong>Total Time:</strong> 2.333 seconds</p>
                        
                        <h4>Extraction Results:</h4>
                        <ul>
                            <li>✅ Applicant Name: John Doe (Confidence: 98%)</li>
                            <li>✅ Company: ABC Manufacturing Corp (Confidence: 97%)</li>
                            <li>✅ Coverage Amount: $500,000 (Confidence: 99%)</li>
                            <li>✅ Premium: $2,100 (Confidence: 98%)</li>
                            <li>✅ Property Address: 123 Industrial Blvd (Confidence: 96%)</li>
                            <li>⚠️ Secondary Contact: Not found</li>
                            <li>⚠️ Additional Locations: Not specified</li>
                        </ul>
                        
                        <h4>Data Quality Score:</h4>
                        <p>Overall: 94% (23/25 required fields populated with high confidence)</p>
                    `
                },
                3: {
                    title: "Risk Assessment - Agent C",
                    details: `
                        <h3>Risk Assessment Agent</h3>
                        <p><strong>Agent ID:</strong> RISK-ASSESS-003</p>
                        <p><strong>Processing Start:</strong> 2025-09-27 14:23:18.401</p>
                        <p><strong>Processing End:</strong> 2025-09-27 14:23:22.567</p>
                        <p><strong>Total Time:</strong> 4.166 seconds</p>
                        
                        <h4>Risk Factors Analyzed:</h4>
                        <ul>
                            <li>🏢 Building Age (1998): Moderate risk (-5 points)</li>
                            <li>🔥 Fire Protection: Excellent (+15 points)</li>
                            <li>🛡️ Security Systems: Good (+10 points)</li>
                            <li>📍 Location Risk: Moderate (-3 points)</li>
                            <li>📈 Industry Type: Standard (0 points)</li>
                            <li>💼 Claims History: Excellent (+12 points)</li>
                        </ul>
                        
                        <h4>Final Risk Score: 87/100 (Low Risk)</h4>
                        <p><strong>Recommendation:</strong> Approve for standard terms</p>
                        <p><strong>Premium Adjustment:</strong> None required (within standard range)</p>
                    `
                },
                4: {
                    title: "Decision Engine - Agent D",
                    details: `
                        <h3>Decision Engine</h3>
                        <p><strong>Agent ID:</strong> DECISION-004</p>
                        <p><strong>Processing Start:</strong> 2025-09-27 14:23:22.634</p>
                        <p><strong>Processing End:</strong> 2025-09-27 14:23:23.891</p>
                        <p><strong>Total Time:</strong> 1.257 seconds</p>
                        
                        <h4>Business Rules Applied:</h4>
                        <ul>
                            <li>✅ Coverage amount within limits ($500K ≤ $1M)</li>
                            <li>✅ Risk score above threshold (87 ≥ 80)</li>
                            <li>✅ Premium calculation verified ($2,100 ±5%)</li>
                            <li>✅ Property type approved (Manufacturing)</li>
                            <li>✅ Geographic area cleared (Chicago, IL)</li>
                            <li>✅ Applicant background verified</li>
                        </ul>
                        
                        <h4>Decision Output:</h4>
                        <p><strong>Recommendation:</strong> AUTO-BIND</p>
                        <p><strong>Confidence Level:</strong> 97%</p>
                        <p><strong>Manual Review Required:</strong> NO</p>
                        <p><strong>Expected Processing:</strong> Automatic approval within SLA</p>
                    `
                },
                5: {
                    title: "Human Review - Bypassed",
                    details: `
                        <h3>Human Review Process</h3>
                        <p><strong>Status:</strong> BYPASSED - Auto-approval criteria met</p>
                        <p><strong>Threshold Requirement:</strong> Risk score ≥ 85 AND Confidence ≥ 95%</p>
                        <p><strong>Actual Values:</strong> Risk score: 87, Confidence: 97%</p>
                        
                        <h4>Auto-Approval Justification:</h4>
                        <ul>
                            <li>✅ All mandatory fields populated</li>
                            <li>✅ Risk assessment within acceptable parameters</li>
                            <li>✅ No fraud indicators detected</li>
                            <li>✅ Premium calculation verified</li>
                            <li>✅ Compliance requirements met</li>
                        </ul>
                        
                        <h4>Fallback Process:</h4>
                        <p>If manual review were required, this submission would be assigned to Senior Underwriter Sarah Mitchell with an expected turnaround of 2-4 hours.</p>
                        
                        <h4>Audit Trail Complete:</h4>
                        <p>All processing steps logged and available for compliance review. Total processing time: 8.7 seconds.</p>
                    `
                }
            };
            
            const modal = document.getElementById('stepModal');
            const modalContent = document.getElementById('modalContent');
            
            modalContent.innerHTML = stepDetails[stepNumber].details;
            modal.style.display = 'block';
        }
        
        function closeModal() {
            document.getElementById('stepModal').style.display = 'none';
        }
        
        // Admin workflow functions
        function saveWorkflow() {
            alert('✅ Workflow configuration saved successfully!\n\nAll agents updated with new parameters.\nChanges will take effect for new submissions.');
        }
        
        function testWorkflow() {
            alert('🧪 Starting workflow test...\n\nTest submission will be processed through all agents.\nResults will be available in the audit panel within 2 minutes.');
        }
        
        function resetWorkflow() {
            if (confirm('Are you sure you want to reset the workflow to default configuration?\n\nThis will undo all custom changes.')) {
                alert('🔄 Workflow reset to default configuration.\n\nAll agents restored to original settings.');
            }
        }

        function installAgent(agentType) {
            const agents = {
                premium: 'Premium Calculator Plus',
                language: 'Multi-Language Processor',
                analytics: 'Predictive Analytics',
                compliance: 'Compliance Guardian'
            };

            alert(`🚀 Installing ${agents[agentType]}...\n\n• Downloading agent package\n• Configuring with your workflow\n• Running compatibility tests\n• Integrating with existing agents\n\nInstallation complete! Agent is now available in your workflow builder.`);

            // Simulate installation by changing button
            event.target.innerHTML = '✅ INSTALLED';
            event.target.style.background = '#28a745';
            event.target.onclick = null;
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('stepModal');
            if (event.target === modal) {
                closeModal();
            }
        }
        
        // Simulate real-time updates
        setInterval(() => {
            // Update dashboard metrics randomly
            const kpiCards = document.querySelectorAll('.kpi-number');
            if (kpiCards.length > 0 && document.getElementById('dashboard').classList.contains('active')) {
                const submissionsCard = kpiCards[0];
                const currentValue = parseInt(submissionsCard.textContent);
                if (Math.random() < 0.1) { // 10% chance to update
                    submissionsCard.textContent = currentValue + 1;
                }
            }
        }, 5000);
    </script>

<!-- OpenRobo one-pager snippet -->
<div style="position: fixed; left: 20px; bottom: 20px; background: rgba(255,255,255,0.95); padding: 12px 16px; border-radius: 8px; box-shadow: 0 8px 24px rgba(0,0,0,0.12); font-size: 13px; max-width: 420px;">
  <strong>OpenRobo — Agentic Insurance Suite</strong><br>
  Reinvent underwriting & claims with modular AI agents, fairness monitoring, and private AI hosting. <br>
  <em>Contact:</em> <EMAIL>
</div>

</body>
</html>